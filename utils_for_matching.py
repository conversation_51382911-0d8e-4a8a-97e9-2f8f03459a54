import numpy as np
import pandas as pd
import torch
from sklearn.metrics import roc_auc_score, average_precision_score, roc_curve, confusion_matrix, balanced_accuracy_score

# 化学分子编码设置
Drug_Max_Length = 100
atom_dict = {"#": 29, "%": 30, ")": 31, "(": 1, "+": 32, "-": 33, "/": 34, ".": 2, "1": 35, "0": 3,
            "3": 36, "2": 4, "5": 37, "4": 5, "7": 38, "6": 6, "9": 39, "8": 7, "=": 40, "A": 41,
            "@": 8, "C": 42, "B": 9, "E": 43, "D": 10, "G": 44, "F": 11, "I": 45, "H": 12, "K": 46,
            "M": 47, "L": 13, "O": 48, "N": 14, "P": 15, "S": 49, "R": 16, "U": 50, "T": 17, "W": 51,
            "V": 18, "Y": 52, "[": 53, "Z": 19, "]": 54, "\\": 20, "a": 55, "c": 56, "b": 21, "e": 57,
            "d": 22, "g": 58, "f": 23, "i": 59, "h": 24, "m": 60, "l": 25, "o": 61, "n": 26, "s": 62,
            "r": 27, "u": 63, "t": 28, "y": 64}


def smiles_to_tensor(smiles_list):
    """将SMILES字符串列表转换为张量"""
    tensor_list = []
    for smiles in smiles_list:
        s = np.zeros(Drug_Max_Length)
        for j in range(min(len(smiles), Drug_Max_Length)):
            if smiles[j] in atom_dict:
                s[j] = atom_dict[smiles[j]]
        tensor_list.append(s.tolist())
    return torch.from_numpy(np.array(tensor_list, dtype=int)).long()


def load_chemical_data(file_path):
    """加载化学品活性数据"""
    df = pd.read_excel(file_path)
    smiles_list = df['smiles'].tolist()
    labels = df['FishLC50'].tolist()
    return smiles_list, labels


def evaluate_model(y_pred, y_label):
    """评估模型性能"""
    y_pred = np.array(y_pred, dtype=float)
    y_label = np.array(y_label, dtype=int)

    # 计算AUC-ROC
    auc_roc = roc_auc_score(y_label, y_pred)

    # 计算AUPR
    aupr = average_precision_score(y_label, y_pred)

    # 获取最优阈值
    fpr, tpr, thresholds = roc_curve(y_label, y_pred)
    precision = tpr / (tpr + fpr + 0.00001)
    f1 = 2 * precision * tpr / (tpr + precision + 0.00001)
    thred_optim = thresholds[5:][np.argmax(f1[5:])]
    y_pred_binary = [1 if i else 0 for i in (y_pred >= thred_optim)]

    # 计算混淆矩阵和相关指标
    cm = confusion_matrix(y_label, y_pred_binary)
    total = sum(sum(cm))
    accuracy = (cm[0, 0] + cm[1, 1]) / total
    sensitivity = cm[1, 1] / (cm[1, 0] + cm[1, 1])
    specificity = cm[0, 0] / (cm[0, 0] + cm[0, 1])

    # 计算平衡准确率
    balanced_acc = balanced_accuracy_score(y_label, y_pred_binary)

    return auc_roc, aupr, accuracy, sensitivity, specificity, balanced_acc


