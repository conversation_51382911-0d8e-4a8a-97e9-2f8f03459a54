import random
import os
import pandas as pd
import numpy as np
import math
import torch
import torch.nn as nn
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split
from utils_for_matching import evaluate_model

#################基础设置################
LR = 0.0001
SEED = 3142
BATCH_SIZE = 128
EPOCH = 100
Feature_Size = 256

###############化学分子编码模块###############
# SMILES_Coding
DSC_Kernel_Num = 32
DSC_Kernel_Size = 8
Drug_SMILES_Input_Size = 128

###############数据处理设置################
Drug_Max_Length = 100
atom_dict = {"#": 29, "%": 30, ")": 31, "(": 1, "+": 32, "-": 33, "/": 34, ".": 2, "1": 35, "0": 3,
            "3": 36, "2": 4, "5": 37, "4": 5, "7": 38, "6": 6, "9": 39, "8": 7, "=": 40, "A": 41,
            "@": 8, "C": 42, "B": 9, "E": 43, "D": 10, "G": 44, "F": 11, "I": 45, "H": 12, "K": 46,
            "M": 47, "L": 13, "O": 48, "N": 14, "P": 15, "S": 49, "R": 16, "U": 50, "T": 17, "W": 51,
            "V": 18, "Y": 52, "[": 53, "Z": 19, "]": 54, "\\": 20, "a": 55, "c": 56, "b": 21, "e": 57,
            "d": 22, "g": 58, "f": 23, "i": 59, "h": 24, "m": 60, "l": 25, "o": 61, "n": 26, "s": 62,
            "r": 27, "u": 63, "t": 28, "y": 64}

Atom_Dic_Length = 64


class GraphConvolution(nn.Module):
    def __init__(self, in_size, out_size,):
        super(GraphConvolution, self).__init__()
        self.in_size = in_size
        self.out_size = out_size
        self.weight = nn.Parameter(torch.FloatTensor(in_size, out_size))
        self.reset_parameters()

    def reset_parameters(self):
        stdv = 1. / math.sqrt(self.weight.size(1))
        self.weight.data.uniform_(-stdv, stdv)

    def forward(self, x, a):
        support = torch.mm(x, self.weight)  # X*W
        r = torch.mm(a, support)    # A*X*W
        return r


class ChemicalSMILESCoding(nn.Module):
    def __init__(self, hid_dim=Drug_SMILES_Input_Size, out_dim=Feature_Size, vocab_size=Atom_Dic_Length,
                 channel=DSC_Kernel_Num, kernel_size=DSC_Kernel_Size):
        super(ChemicalSMILESCoding, self).__init__()
        self.embedding = nn.Embedding(vocab_size, embedding_dim=hid_dim)
        self.conv1 = nn.Conv1d(hid_dim, channel, kernel_size, padding=kernel_size-1)
        self.conv2 = nn.Conv1d(channel, channel*2, kernel_size, padding=kernel_size-1)
        self.conv3 = nn.Conv1d(channel*2, channel*4, kernel_size, padding=kernel_size-1)
        self.act = nn.LeakyReLU(0.2)
        self.globalmaxpool = nn.AdaptiveMaxPool1d(1)
        self.fc1 = nn.Linear(channel*4, out_dim)

    def forward(self, x):
        x = self.embedding(x)
        x = x.permute(0, 2, 1)
        x = self.conv1(x)
        x = self.act(x)
        x = self.conv2(x)
        x = self.act(x)
        x = self.conv3(x)
        x = self.act(x)
        x = self.globalmaxpool(x)
        x = x.squeeze(-1)
        x = self.fc1(x)
        return x


class ChemicalActivityPredictor(nn.Module):
    def __init__(self, input_dim=Feature_Size):
        super(ChemicalActivityPredictor, self).__init__()
        self.chemical_encoder = ChemicalSMILESCoding()
        self.fc1 = nn.Linear(input_dim, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 2)
        self.act1 = nn.LeakyReLU(0.2)
        self.act2 = nn.Tanh()
        self.dropout = nn.Dropout(0.2)

    def forward(self, smiles):
        chemical_features = self.chemical_encoder(smiles)
        x = self.fc1(chemical_features)
        x = self.act1(x)
        x = self.dropout(x)
        x = self.fc2(x)
        x = self.act2(x)
        x = self.dropout(x)
        x = self.fc3(x)
        return x


def seed_torch(seed):
    random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.benchmark = False
    torch.backends.cudnn.deterministic = True


def smiles_to_tensor(smiles_list):
    """将SMILES字符串列表转换为张量"""
    tensor_list = []
    for smiles in smiles_list:
        s = np.zeros(Drug_Max_Length)
        for j in range(min(len(smiles), Drug_Max_Length)):
            if smiles[j] in atom_dict:
                s[j] = atom_dict[smiles[j]]
        tensor_list.append(s.tolist())
    return torch.from_numpy(np.array(tensor_list, dtype=int)).long()


def load_data(file_path):
    """加载Excel数据"""
    df = pd.read_excel(file_path)
    smiles_list = df['smiles'].tolist()
    labels = df['FishLC50'].tolist()
    return smiles_list, labels





if __name__ == '__main__':
    seed_torch(SEED)

    # 加载数据
    print('Loading data...')
    smiles_list, labels = load_data('input_data.xlsx')

    # 数据预处理
    smiles_tensor = smiles_to_tensor(smiles_list)
    labels_tensor = torch.from_numpy(np.array(labels, dtype=int)).long()

    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        smiles_tensor, labels_tensor, test_size=0.2, random_state=SEED, stratify=labels_tensor
    )

    print(f'Training samples: {len(X_train)}, Test samples: {len(X_test)}')
    print(f'Training labels distribution: {torch.bincount(y_train)}')
    print(f'Test labels distribution: {torch.bincount(y_test)}')

    # 创建数据加载器
    train_dataset = TensorDataset(X_train, y_train)
    test_dataset = TensorDataset(X_test, y_test)
    train_dataloader = DataLoader(dataset=train_dataset, batch_size=BATCH_SIZE, shuffle=True)
    test_dataloader = DataLoader(dataset=test_dataset, batch_size=BATCH_SIZE, shuffle=False)

    # 初始化模型
    model = ChemicalActivityPredictor()
    loss_func = nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=LR)

    if torch.cuda.is_available():
        model = model.cuda()
        print('Using GPU for training')

    print('Start training...')
    best_auc = 0
    best_model = None

    for epoch in range(EPOCH):
        model.train()
        total_loss = 0

        for step, (batch_smiles, batch_labels) in enumerate(train_dataloader):
            if torch.cuda.is_available():
                batch_smiles = batch_smiles.cuda()
                batch_labels = batch_labels.cuda()

            optimizer.zero_grad()
            predictions = model(batch_smiles)
            loss = loss_func(predictions, batch_labels)
            loss.backward()
            optimizer.step()

            total_loss += loss.item()

            if step % 10 == 0:
                print(f'Epoch: {epoch}, Step: {step}/{len(train_dataloader)}, Loss: {loss.item():.6f}')

        # 验证
        model.eval()
        val_predictions = []
        val_labels = []

        with torch.no_grad():
            for batch_smiles, batch_labels in test_dataloader:
                if torch.cuda.is_available():
                    batch_smiles = batch_smiles.cuda()
                    batch_labels = batch_labels.cuda()

                predictions = model(batch_smiles)
                val_predictions.extend(torch.softmax(predictions, dim=1)[:, 1].cpu().numpy())
                val_labels.extend(batch_labels.cpu().numpy())

        # 评估性能
        auc_roc, aupr, accuracy, sensitivity, specificity, balanced_acc = evaluate_model(val_predictions, val_labels)
        print(f'Epoch {epoch}: AUC-ROC = {auc_roc:.4f}, AUPR = {aupr:.4f}, ACC = {accuracy:.4f}, Balanced ACC = {balanced_acc:.4f}')

        if auc_roc > best_auc:
            best_auc = auc_roc
            best_model = model.state_dict().copy()
            print(f'New best model! AUC-ROC = {auc_roc:.4f}')

    # 保存最佳模型
    torch.save(best_model, 'models/chemical_activity_predictor.pth')
    print(f'Training completed. Best AUC: {best_auc:.4f}')
