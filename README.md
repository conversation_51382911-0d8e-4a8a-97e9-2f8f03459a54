# Chemical Activity Predictor

A simplified deep learning framework for predicting chemical compound activity based on SMILES molecular representations. This framework is adapted from the DCGCN architecture, focusing specifically on chemical activity prediction.

## Features

- **SMILES-based molecular encoding**: Uses 1D convolutional neural networks to encode chemical structures from SMILES strings
- **Binary activity prediction**: Predicts whether a chemical compound is active (1) or inactive (0)
- **Simple and efficient**: Streamlined architecture focused on chemical activity prediction
- **Easy to use**: Simple data format (Excel file with SMILES and labels)

## System Requirements

- Python 3.8+
- PyTorch 2.1.1+
- Standard computer with sufficient RAM
- Optional: CUDA-compatible GPU for faster training

## Installation

1. Create a new conda environment:
```bash
conda create --name chemical_predictor python=3.8
conda activate chemical_predictor
```

2. Install required packages:
```bash
pip install -r requirements.txt
```

## Data Format

The input data should be an Excel file (`input_data.xlsx`) with two columns:
- `smiles`: SMILES representation of chemical compounds
- `FishLC50`: Activity labels (0 for inactive, 1 for active)

Example:
```
smiles                                          FishLC50
C[C@]12CC[C@@H]3c4ccc(O)cc4CC[C@H]3[C@@H]1...  1
C#C[C@]1(O)CC[C@H]2[C@@H]3CCc4cc(O)ccc4...     1
CN(C)[C@@H]1C(=O)C(C(N)=O)=C(O)[C@@]2(O)...    0
```

## Usage

### Training
```bash
python main.py
```
This will:
- Load data from `input_data.xlsx`
- Split data into training and test sets (80/20)
- Train the model for the specified number of epochs
- Save the best model to `models/chemical_activity_predictor.pth`

### Prediction
```bash
python predict.py
```
This will:
- Load the trained model
- Make predictions on the input data
- Save results to `prediction_results.csv`

### Evaluation
```bash
python evalute.py
```
This will:
- Load the trained model
- Evaluate performance on the input data
- Display metrics (AUC, AUPR, Accuracy, Sensitivity, Specificity)

## Model Architecture

The framework uses a simplified neural network architecture:

1. **Chemical SMILES Encoding**:
   - Embedding layer for SMILES characters
   - 1D convolutional layers with increasing channels
   - Global max pooling
   - Fully connected layer

2. **Activity Prediction**:
   - Multi-layer perceptron with dropout
   - Binary classification output

## Configuration

Key parameters can be modified in the script files:
- `BATCH_SIZE`: Batch size for training (default: 128)
- `EPOCH`: Number of training epochs (default: 100)
- `LR`: Learning rate (default: 0.0001)
- `Feature_Size`: Feature dimension (default: 256)

## Output Files

- `models/chemical_activity_predictor.pth`: Trained model weights
- `prediction_results.csv`: Prediction results with SMILES, true labels, and predicted probabilities

## Example Output

During training, you'll see output like:
```
Epoch 0: AUC-ROC = 0.7234, AUPR = 0.6891, ACC = 0.6750, Balanced ACC = 0.6823
Epoch 1: AUC-ROC = 0.7456, AUPR = 0.7123, ACC = 0.6950, Balanced ACC = 0.7012
New best model! AUC-ROC = 0.7456
```

During evaluation, you'll see:
```
Evaluation Results:
AUC-ROC: 0.8234
AUPR: 0.7891
Accuracy: 0.7650
Balanced Accuracy: 0.7723
Sensitivity: 0.7890
Specificity: 0.7556
```

## Performance Metrics

The framework evaluates models using:
- **AUC-ROC**: Area Under the ROC Curve (Receiver Operating Characteristic)
- **AUPR**: Area Under the Precision-Recall Curve
- **Accuracy**: Overall classification accuracy
- **Balanced Accuracy**: Average of sensitivity and specificity (better for imbalanced datasets)
- **Sensitivity**: True positive rate (recall)
- **Specificity**: True negative rate
