import numpy as np
import torch
import torch.nn as nn
import pandas as pd
from torch.utils.data import TensorDataset, DataLoader
from utils_for_matching import smiles_to_tensor, load_chemical_data, evaluate_model

# 基础设置
BATCH_SIZE = 512
Feature_Size = 256
DSC_Kernel_Num = 32
DSC_Kernel_Size = 8
Drug_SMILES_Input_Size = 128
Drug_Max_Length = 100

atom_dict = {"#": 29, "%": 30, ")": 31, "(": 1, "+": 32, "-": 33, "/": 34, ".": 2, "1": 35, "0": 3,
            "3": 36, "2": 4, "5": 37, "4": 5, "7": 38, "6": 6, "9": 39, "8": 7, "=": 40, "A": 41,
            "@": 8, "C": 42, "B": 9, "E": 43, "D": 10, "G": 44, "F": 11, "I": 45, "H": 12, "<PERSON>": 46,
            "M": 47, "L": 13, "O": 48, "N": 14, "P": 15, "S": 49, "R": 16, "U": 50, "T": 17, "W": 51,
            "V": 18, "Y": 52, "[": 53, "Z": 19, "]": 54, "\\": 20, "a": 55, "c": 56, "b": 21, "e": 57,
            "d": 22, "g": 58, "f": 23, "i": 59, "h": 24, "m": 60, "l": 25, "o": 61, "n": 26, "s": 62,
            "r": 27, "u": 63, "t": 28, "y": 64}

Atom_Dic_Length = 64


# 从main.py导入模型定义
from main import ChemicalActivityPredictor


if __name__ == '__main__':
    # 加载数据
    print('Loading data for evaluation...')
    smiles_list, labels = load_chemical_data('input_data.xlsx')

    # 数据预处理
    smiles_tensor = smiles_to_tensor(smiles_list)
    labels_tensor = torch.from_numpy(np.array(labels, dtype=int)).long()

    # 创建数据加载器
    dataset = TensorDataset(smiles_tensor, labels_tensor)
    dataloader = DataLoader(dataset=dataset, batch_size=BATCH_SIZE, shuffle=False)

    # 加载模型
    model = ChemicalActivityPredictor()
    model.load_state_dict(torch.load('models/chemical_activity_predictor.pth'))
    model.eval()

    if torch.cuda.is_available():
        model = model.cuda()
        print('Using GPU for evaluation')

    # 评估
    predictions = []
    true_labels = []

    print('Starting evaluation...')
    with torch.no_grad():
        for step, (batch_smiles, batch_labels) in enumerate(dataloader):
            if torch.cuda.is_available():
                batch_smiles = batch_smiles.cuda()
                batch_labels = batch_labels.cuda()

            batch_pred = model(batch_smiles)
            pred_probs = torch.softmax(batch_pred, dim=1)

            predictions.extend(pred_probs[:, 1].cpu().numpy())
            true_labels.extend(batch_labels.cpu().numpy())

            if step % 10 == 0:
                print(f'Processed {step}/{len(dataloader)} batches')

    # 计算评估指标
    auc_score, aupr, accuracy, sensitivity, specificity = evaluate_model(predictions, true_labels)

    print(f'\nEvaluation Results:')
    print(f'AUC: {auc_score:.4f}')
    print(f'AUPR: {aupr:.4f}')
    print(f'Accuracy: {accuracy:.4f}')
    print(f'Sensitivity: {sensitivity:.4f}')
    print(f'Specificity: {specificity:.4f}')

